# 学习工具开发设计文档

**项目名称**: 自动化学习辅助系统  
**基于框架**: PySide6-Fluent-Widgets  
**文档版本**: v1.0  
**创建日期**: 2025-01-14  

## 1. 项目分析阶段

### 1.1 现有脚手架结构分析

#### 核心目录结构
```
app/
├── common/           # 公共模块
│   ├── config.py    # 配置管理系统
│   ├── style_sheet.py # 样式表管理
│   ├── signal_bus.py # 信号总线
│   └── translator.py # 国际化支持
├── view/            # 界面模块
│   ├── main_window.py # 主窗口
│   ├── home_interface.py # 首页
│   ├── blank_interface.py # 空白页模板
│   └── setting_interface.py # 设置页
├── components/      # 自定义组件
└── config/         # 配置文件
    └── config.json # 应用配置
```

#### 现有配置系统特点
- 基于`QConfig`的配置管理
- 支持主题切换、DPI缩放、语言设置
- 配置文件自动序列化/反序列化
- 实时配置更新机制

#### 现有界面组件模式
- 继承自`MSFluentWindow`的主窗口
- 使用`ScrollArea`的滚动界面
- 统一的样式表管理系统
- 信号槽机制的事件处理

### 1.2 扩展模块规划

#### 新增目录结构
```
app/
├── xueyuan/         # 学习工具模块
│   ├── common/      # 学习工具公共模块
│   ├── components/  # 学习工具界面组件
│   ├── view/        # 学习工具界面
│   ├── config/      # 学习工具配置
│   ├── core/        # 核心业务逻辑
│   └── database/    # 数据库操作
├── ocr/             # OCR识别模块
│   ├── ddddocr/     # Ddddocr引擎
│   ├── baidu/       # 百度OCR引擎
│   └── common/      # OCR公共模块
└── data/            # 数据目录
    ├── config/      # 配置文件
    ├── database/    # 数据库文件
    └── logs/        # 日志文件
```

## 2. 需求分析

### 2.1 功能模块划分

#### 核心功能模块
1. **用户管理模块**
   - 批量导入用户账号
   - 用户信息编辑
   - 状态管理和统计

2. **自动化学习模块**
   - Playwright浏览器自动化
   - 登录流程自动化
   - 课程学习自动化
   - 视频播放监控

3. **OCR识别模块**
   - Ddddocr主引擎
   - 百度OCR备选引擎
   - 验证码识别和处理

4. **数据管理模块**
   - SQLite3数据存储
   - 用户数据管理
   - 课程数据管理
   - 学习记录管理

5. **日志系统模块**
   - 多级别日志记录
   - 实时日志查看
   - 日志导出和清理

6. **线程池模块**
   - 并发控制
   - 任务队列管理
   - 异常处理和重试

### 2.2 数据结构设计

#### 用户数据表 (users)
```sql
CREATE TABLE users (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    phone TEXT UNIQUE NOT NULL,           -- 手机号码
    password TEXT NOT NULL,               -- 密码
    name TEXT,                           -- 姓名
    status TEXT DEFAULT '未开始',         -- 状态
    complete_status TEXT DEFAULT '1',     -- 完成状态
    online_total_credit REAL DEFAULT 0,  -- 总学时
    compulsory_credit REAL DEFAULT 0,    -- 必修学时
    electives_credit REAL DEFAULT 0,     -- 选修学时
    last_login_time DATETIME,            -- 最后登录时间
    error_message TEXT,                  -- 错误信息
    progress REAL DEFAULT 0,             -- 进度
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);
```

#### 课程数据表 (courses)
```sql
CREATE TABLE courses (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    user_phone TEXT NOT NULL,            -- 用户手机号
    course_type TEXT NOT NULL,           -- 课程类型(必修课/选修课)
    name TEXT NOT NULL,                  -- 课程名称
    course_id TEXT NOT NULL,             -- 课程ID
    completed TEXT DEFAULT '0',          -- 完成状态
    credit INTEGER DEFAULT 0,            -- 学时
    percentage TEXT DEFAULT '0',         -- 完成进度
    courseware_id TEXT,                  -- 课件ID
    video_url TEXT,                      -- 视频URL
    completed_date DATE,                 -- 完成日期
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_phone) REFERENCES users(phone)
);
```

#### 日志数据表 (logs)
```sql
CREATE TABLE logs (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    level TEXT NOT NULL,                 -- 日志级别
    message TEXT NOT NULL,               -- 日志消息
    module TEXT,                         -- 模块名称
    user_phone TEXT,                     -- 相关用户
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
);
```

## 3. 技术方案设计

### 3.1 系统架构图

```
┌─────────────────────────────────────────────────────────────┐
│                    主界面 (MainWindow)                        │
├─────────────────────────────────────────────────────────────┤
│  学习控制  │  用户管理  │  进度监控  │  日志查看  │  学习设置  │
├─────────────────────────────────────────────────────────────┤
│                    业务逻辑层                                │
├─────────────────────────────────────────────────────────────┤
│  学习引擎  │  OCR引擎  │  数据管理  │  线程池   │  日志系统  │
├─────────────────────────────────────────────────────────────┤
│                    数据访问层                                │
├─────────────────────────────────────────────────────────────┤
│  SQLite3  │  配置文件  │  日志文件  │  临时数据  │  缓存数据  │
└─────────────────────────────────────────────────────────────┘
```

### 3.2 核心模块设计

#### 3.2.1 学习配置系统
```python
class StudyConfig(QConfig):
    # 系统配置
    asyncLogin = ConfigItem("System", "AsyncLogin", True, BoolValidator())
    compulsoryCourses = RangeConfigItem("System", "CompulsoryCourses", 20, RangeValidator(1, 100))
    electiveCourses = RangeConfigItem("System", "ElectiveCourses", 20, RangeValidator(1, 100))
    concurrentCount = RangeConfigItem("System", "ConcurrentCount", 2, RangeValidator(1, 10))
    
    # 浏览器配置
    browserType = OptionsConfigItem("Browser", "BrowserType", "chrome", OptionsValidator(["chrome", "firefox", "edge"]))
    headless = ConfigItem("Browser", "Headless", False, BoolValidator())
    
    # OCR配置
    ocrApiKey = ConfigItem("OCR", "ApiKey", "", str)
    ocrSecretKey = ConfigItem("OCR", "SecretKey", "", str)
    
    # 日志配置
    logLevel = OptionsConfigItem("Logging", "LogLevel", "INFO", OptionsValidator(["DEBUG", "INFO", "WARNING", "ERROR"]))
```

#### 3.2.2 自动化学习引擎
```python
class StudyEngine:
    """学习引擎核心类"""
    
    async def login(self, phone: str, password: str) -> bool:
        """自动登录"""
        
    async def get_user_status(self) -> dict:
        """获取用户学习状态"""
        
    async def get_courses(self) -> dict:
        """获取课程列表"""
        
    async def study_course(self, course_id: str) -> bool:
        """学习指定课程"""
        
    async def monitor_video_progress(self) -> dict:
        """监控视频播放进度"""
```

#### 3.2.3 OCR识别系统
```python
class OCRManager:
    """OCR管理器"""
    
    def __init__(self):
        self.ddddocr_engine = DdddocrEngine()
        self.baidu_engine = BaiduOCREngine()
        
    async def recognize_captcha(self, image_data: bytes) -> str:
        """识别验证码"""
        # 优先使用Ddddocr，失败时使用百度OCR
```

#### 3.2.4 线程池管理
```python
class StudyThreadPool:
    """学习线程池"""
    
    def __init__(self, max_workers: int = 2):
        self.executor = ThreadPoolExecutor(max_workers=max_workers)
        self.tasks = {}
        
    def submit_study_task(self, user_data: dict) -> Future:
        """提交学习任务"""
        
    def get_task_status(self, task_id: str) -> str:
        """获取任务状态"""
```

### 3.3 界面设计方案

#### 3.3.1 主界面布局
```python
class StudyMainInterface(ScrollArea):
    """学习工具主界面"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.pivot = Pivot(self)  # 顶部导航
        self.setupUI()
        
    def setupUI(self):
        # 添加各个子界面到Pivot
        self.addSubInterface(StudyControlInterface(), "学习控制")
        self.addSubInterface(UserManageInterface(), "用户管理")
        self.addSubInterface(ProgressMonitorInterface(), "进度监控")
        self.addSubInterface(LogViewInterface(), "日志查看")
        self.addSubInterface(StudySettingInterface(), "学习设置")
```

#### 3.3.2 用户管理界面
```python
class UserManageInterface(QWidget):
    """用户管理界面"""
    
    def setupUI(self):
        # 顶部操作栏
        self.toolbar = QHBoxLayout()
        self.addUserBtn = PrimaryPushButton("批量添加")
        self.importBtn = PushButton("导入用户")
        self.editBtn = PushButton("编辑用户")
        
        # 用户列表表格
        self.userTable = TableWidget()
        self.setupTable()
        
        # 底部统计栏
        self.statusBar = QHBoxLayout()
        self.totalLabel = BodyLabel("总用户: 0")
        self.waitingLabel = BodyLabel("待学习: 0")
```

#### 3.3.3 学习控制界面
```python
class StudyControlInterface(QWidget):
    """学习控制界面"""
    
    def setupUI(self):
        # 左侧控制区
        self.controlPanel = QVBoxLayout()
        self.progressRing = ProgressRing()  # 进度环
        self.statusLabel = BodyLabel("就绪")
        self.startBtn = PrimaryPushButton("启动学习")
        self.pauseBtn = PushButton("暂停学习")
        self.stopBtn = PushButton("停止学习")
        
        # 右侧进度区
        self.progressTable = TableWidget()
        self.setupProgressTable()
```

## 4. 开发计划

### 4.1 开发阶段划分

#### 第一阶段：基础架构搭建 (3-4天)
1. **配置系统开发**
   - 创建`StudyConfig`配置类
   - 实现配置文件加载和保存
   - 集成到现有配置系统

2. **数据库系统开发**
   - 设计数据库表结构
   - 实现数据访问层(DAO)
   - 数据库初始化和迁移

3. **基础界面框架**
   - 创建学习工具主界面
   - 实现Pivot导航结构
   - 基础样式表开发

#### 第二阶段：核心功能开发 (5-6天)
1. **OCR识别模块**
   - Ddddocr引擎集成
   - 百度OCR引擎集成
   - OCR管理器实现

2. **自动化学习引擎**
   - Playwright浏览器自动化
   - 登录流程实现
   - 状态检查模块

3. **用户管理功能**
   - 用户数据CRUD操作
   - 批量导入功能
   - 用户状态管理

#### 第三阶段：高级功能开发 (4-5天)
1. **课程学习流程**
   - 课程数据获取
   - 视频播放监控
   - 学习进度跟踪

2. **线程池和并发控制**
   - 多任务并发执行
   - 任务队列管理
   - 异常处理和重试

3. **日志系统**
   - 多级别日志记录
   - 日志查看界面
   - 日志导出功能

#### 第四阶段：界面完善和测试 (3-4天)
1. **界面优化**
   - 用户体验优化
   - 响应式布局调整
   - 主题适配完善

2. **功能测试**
   - 单元测试编写
   - 集成测试
   - 性能测试

3. **文档和部署**
   - 用户手册编写
   - 部署脚本准备
   - 版本发布准备

### 4.2 开发优先级

#### 高优先级 (P0)
- 配置系统和数据库
- OCR识别功能
- 基础自动化登录
- 用户管理界面

#### 中优先级 (P1)
- 课程学习流程
- 线程池并发控制
- 日志系统
- 进度监控界面

#### 低优先级 (P2)
- 界面美化优化
- 高级配置选项
- 性能优化
- 扩展功能

### 4.3 工作量预估

| 模块 | 预估工时 | 复杂度 | 风险等级 |
|------|----------|--------|----------|
| 配置系统 | 1天 | 低 | 低 |
| 数据库设计 | 1天 | 中 | 低 |
| OCR识别 | 2天 | 中 | 中 |
| 自动化引擎 | 3天 | 高 | 高 |
| 用户管理 | 2天 | 中 | 低 |
| 课程学习 | 3天 | 高 | 高 |
| 线程池 | 2天 | 中 | 中 |
| 日志系统 | 1天 | 低 | 低 |
| 界面开发 | 3天 | 中 | 低 |
| 测试优化 | 2天 | 中 | 中 |

**总计**: 20-22个工作日

## 5. 风险评估和应对策略

### 5.1 技术风险

#### 高风险项
1. **网站反爬虫机制**
   - 风险: 目标网站可能有反自动化检测
   - 应对: 实现随机延迟、User-Agent轮换、代理支持

2. **验证码识别准确率**
   - 风险: OCR识别失败率较高
   - 应对: 双引擎备份、人工介入机制

3. **网站结构变更**
   - 风险: 目标网站DOM结构改变
   - 应对: 灵活的选择器配置、多重定位策略

#### 中风险项
1. **并发控制复杂性**
   - 风险: 多线程同步问题
   - 应对: 使用成熟的线程池库、充分测试

2. **数据一致性**
   - 风险: 并发操作导致数据不一致
   - 应对: 数据库事务、锁机制

### 5.2 业务风险

1. **合规性风险**
   - 风险: 自动化学习可能违反平台规则
   - 应对: 添加免责声明、用户自主选择

2. **性能风险**
   - 风险: 大量并发可能影响目标服务器
   - 应对: 合理的并发限制、请求频率控制

## 6. 接口设计

### 6.1 核心接口定义

#### 学习引擎接口
```python
class IStudyEngine(ABC):
    @abstractmethod
    async def login(self, credentials: dict) -> bool:
        """登录接口"""
        
    @abstractmethod
    async def get_study_status(self) -> dict:
        """获取学习状态"""
        
    @abstractmethod
    async def start_study(self, course_id: str) -> bool:
        """开始学习"""
```

#### OCR识别接口
```python
class IOCREngine(ABC):
    @abstractmethod
    async def recognize(self, image_data: bytes) -> str:
        """识别验证码"""
        
    @abstractmethod
    def get_confidence(self) -> float:
        """获取识别置信度"""
```

#### 数据访问接口
```python
class IUserDAO(ABC):
    @abstractmethod
    def create_user(self, user_data: dict) -> bool:
        """创建用户"""
        
    @abstractmethod
    def get_user_by_phone(self, phone: str) -> dict:
        """根据手机号获取用户"""
        
    @abstractmethod
    def update_user_status(self, phone: str, status: str) -> bool:
        """更新用户状态"""
```

## 7. 部署和维护

### 7.1 依赖管理
```
requirements.txt:
- PySide6
- qfluentwidgets
- playwright
- ddddocr
- baidu-aip
- sqlite3 (内置)
- asyncio (内置)
```

### 7.2 配置文件模板
```json
{
    "System": {
        "AsyncLogin": true,
        "CompulsoryCourses": 20,
        "ElectiveCourses": 20,
        "ConcurrentCount": 2,
        "DelayTime": 1,
        "RetryCount": 3
    },
    "Browser": {
        "BrowserType": "chrome",
        "Headless": false,
        "DisableImages": false,
        "UserAgent": ""
    },
    "OCR": {
        "PrimaryEngine": "ddddocr",
        "BaiduApiKey": "",
        "BaiduSecretKey": "",
        "Timeout": 10
    },
    "Logging": {
        "LogLevel": "INFO",
        "MaxDays": 30,
        "MaxSizeMB": 100
    }
}
```

### 7.3 目录结构规范
```
project_root/
├── app/                 # 应用程序代码
├── data/               # 数据目录
│   ├── config/         # 配置文件
│   ├── database/       # 数据库文件
│   ├── logs/          # 日志文件
│   └── temp/          # 临时文件
├── docs/              # 文档
├── tests/             # 测试代码
└── requirements.txt   # 依赖列表
```

## 8. 总结

本设计文档基于现有的PySide6-Fluent-Widgets脚手架，充分利用其配置系统、界面组件和样式管理等优势，设计了一个功能完整、架构清晰的自动化学习辅助系统。

### 8.1 设计亮点
1. **模块化设计**: 学习工具和OCR识别独立开发，便于维护
2. **配置驱动**: 所有功能都可通过配置文件控制
3. **异步并发**: 支持多账号并行学习
4. **容错机制**: 完善的异常处理和重试机制
5. **用户友好**: 基于Fluent Design的现代化界面

### 8.2 技术特色
1. **双引擎OCR**: Ddddocr + 百度OCR备份方案
2. **智能并发**: 异步登录避免OCR冲突
3. **实时监控**: 学习进度实时更新
4. **数据持久化**: SQLite3本地数据存储
5. **日志追踪**: 完整的操作日志记录

### 8.3 扩展性考虑
1. **插件化OCR**: 易于添加新的OCR引擎
2. **配置化选择器**: 网站结构变更时易于适配
3. **模块化界面**: 新功能界面易于集成
4. **标准化接口**: 便于功能扩展和测试

请确认此设计方案是否符合您的需求，我将根据您的反馈进行调整，然后开始具体的开发实现。